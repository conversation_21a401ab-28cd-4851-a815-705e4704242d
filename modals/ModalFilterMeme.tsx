"use client";

import React, { useEffect, useMemo, useState } from "react";
import { CloseIcon, Setting, ReloadIcon } from "@/assets/icons";
import { DEXS, getDexLogoUrl, getDexName } from "@/utils/dex";
import { cloneDeep } from "lodash";
import { NumericFormat } from "react-number-format";
import { AppButton } from "@/components";
import AppDrawer from "@/components/AppDrawer";
import clsx from "clsx";
import { useMediaQuery } from "react-responsive";
import { ICheckboxIcon, ICheckedIcon } from "@/public/images";
import { MEME_DEXES } from "@/enums";
import { toastInfo } from "@/libs/toast";
import Image from "next/image";

const transformFilters = (params: any) => {
  const valueInputs = params?.reduce((acc: any, input: any) => {
    if (input.from && input.to) {
      acc[input.value] = `${input.from}-${input.to}`;
    } else if (input.from) {
      acc[input.value] = `${input.from}-`;
    } else if (input.to) {
      acc[input.value] = `0-${input.to}`;
    }
    return acc;
  }, {} as Record<string, string>);

  return { ...valueInputs };
};

const FILTER_VALUE_CONDITIONS = [
  // { name: 'Bonding curve', value: 'bondingCurve', from: '', to: '' },
  // { name: 'Dev holding', value: 'devHolding', from: '', to: '' },
  // { name: 'Holders', value: 'holders', from: '', to: '' },
  { name: "Liquidity", value: "liquidity", from: "", to: "" },
  { name: "Volume", value: "volume", from: "", to: "" },
  { name: "Market Cap", value: "marketCap", from: "", to: "" },
  { name: "Txns", value: "txns", from: "", to: "" },
  // { name: 'Buys', value: 'buys', from: '', to: '' },
  // { name: 'Sells', value: 'sells', from: '', to: '' },
  // { name: 'Token Age (mins)', value: 'tokenAge', from: '', to: '' },
];

const FilterAdvanced = ({
  params,
  setParams,
}: {
  params: any[];
  setParams: (params: any[]) => void;
}) => {
  const [localValueInputs, setLocalValueInputs] = useState(() => {
    const initialInputs = cloneDeep(FILTER_VALUE_CONDITIONS);

    params.forEach((param: any) => {
      const index = initialInputs.findIndex(
        (input) => input.value === param.value
      );
      if (index !== -1) {
        initialInputs[index].from = param.from;
        initialInputs[index].to = param.to;
      }
    });
    return initialInputs;
  });

  useEffect(() => {
    if (!params.length) {
      const initialInputs = cloneDeep(FILTER_VALUE_CONDITIONS);
      setLocalValueInputs(initialInputs);
      return;
    }
    setLocalValueInputs((prevInputs) => {
      const newInputs = cloneDeep(prevInputs);
      params.forEach((param: any) => {
        const index = newInputs.findIndex(
          (input) => input.value === param.value
        );
        if (index !== -1) {
          newInputs[index].from = param.from;
          newInputs[index].to = param.to;
        }
      });
      return newInputs;
    });
  }, [params]);

  const handleValueInputChange = (
    index: number,
    field: "from" | "to",
    value: string
  ) => {
    const newInputs = [...localValueInputs];
    newInputs[index][field] = value;
    setLocalValueInputs(newInputs);
    const paramFilters = localValueInputs.filter(
      (item) => item.from !== "" || item.to !== ""
    );
    setParams(paramFilters);
  };

  return (
    <div>
      <div className="flex w-full flex-col gap-2">
        {localValueInputs.map((item: any, index) => {
          return (
            <div
              key={item.value}
              className="flex items-center justify-between gap-2"
            >
              <div className="body-sm-regular-12 text-white-800">
                {item.name}
              </div>
              <div className="flex items-center gap-2">
                <div className="border-white-100 bg-black-900 body-sm-regular-12 text-white-300 flex w-[80px] items-center gap-1 rounded-[4px] border p-2">
                  <NumericFormat
                    value={localValueInputs[index].from}
                    onValueChange={({ value }) =>
                      handleValueInputChange(index, "from", value)
                    }
                    placeholder="Min"
                    thousandSeparator=","
                    valueIsNumericString
                    className="body-sm-regular-12 text-neutral-0 placeholder:text-white-300 w-[50px] bg-transparent outline-none"
                  />
                  {(item.value === "liquidity" ||
                    item.value === "volume" ||
                    item.value === "marketCap") &&
                    "$"}
                </div>

                <div className="border-white-100 bg-black-900 body-sm-regular-12 text-white-300 flex w-[80px] items-center gap-1 rounded-[4px] border p-2">
                  <NumericFormat
                    placeholder="Max"
                    value={localValueInputs[index].to}
                    onValueChange={({ value }) =>
                      handleValueInputChange(index, "to", value)
                    }
                    thousandSeparator=","
                    valueIsNumericString
                    className="body-sm-regular-12 text-neutral-0 placeholder:text-white-300 w-[50px] bg-transparent outline-none"
                  />
                  {(item.value === "liquidity" ||
                    item.value === "volume" ||
                    item.value === "marketCap") &&
                    "$"}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

interface IDataParams {
  dexes: string[];
  advancedFilters: any[];
}

export const ModalFilterMeme = ({
  isOpen,
  onClose,
  setParams,
  dataParams,
  setDataParams,
  title,
}: {
  isOpen: boolean;
  title: string;
  onClose: () => void;
  dataParams: IDataParams;
  setParams: (params: any) => void;
  setDataParams: (params: IDataParams) => void;
}) => {
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  // const customStyles = {
  //   content: {
  //     top: 0,
  //     left: 'auto',
  //     right: 0,
  //     bottom: 0,
  //     border: 0,
  //     padding: 0,
  //     height: '100vh',
  //     background: '#141518',
  //     overflow: 'inherit',
  //   },
  //   overlay: {
  //     background: 'rgba(8, 9, 12, 0.70)',
  //     zIndex: 999,
  //   },
  // };

  const [listDexSelected, setListDexSelected] = useState<string[]>([]);
  const [paramsAdvanced, setSetParamsAdvanced] = useState<any>([]);

  const isSelectedAllDex = useMemo(() => {
    if (!listDexSelected.length) return false;
    return MEME_DEXES.every((item) => listDexSelected.includes(item));
  }, [listDexSelected]);

  const onSelectAllDex = () => {
    if (isSelectedAllDex) {
      setListDexSelected([]);
      return;
    }
    setListDexSelected(MEME_DEXES);
  };

  const onSelectDex = (dex: string) => {
    let data = listDexSelected;
    if (listDexSelected.includes(dex)) {
      data = data.filter((item) => item !== dex);
    } else {
      data = data.concat([dex]);
    }
    setListDexSelected(data);
  };

  const onRefresh = () => {
    setListDexSelected([]);
    setSetParamsAdvanced([]);
    setParams({});
    setDataParams({
      dexes: MEME_DEXES,
      advancedFilters: [],
    });
    onClose();
  };

  useEffect(() => {
    setListDexSelected(dataParams.dexes);
    setSetParamsAdvanced(dataParams.advancedFilters);
  }, [dataParams]);

  const onFilter = () => {
    console.log(listDexSelected, "listDexSelected");
    if (!listDexSelected.length) {
      toastInfo("Warning!", "At least one dex or platform should be enable");
      return;
    }
    setDataParams({
      dexes: listDexSelected,
      advancedFilters: paramsAdvanced,
    });

    const dexes = listDexSelected.join(",");
    const paramsAdvancedFormat = transformFilters(paramsAdvanced);

    setParams({
      dexes: dexes,
      ...paramsAdvancedFormat,
    });
    onClose();
  };

  const _renderCheckbox = (checked?: boolean) => {
    return checked ? (
      <img src={ICheckedIcon.src} alt="Checked" />
    ) : (
      <img src={ICheckboxIcon.src} alt="Checkbox" />
    );
  };

  return (
    <>
      <AppDrawer
        isOpen={isOpen}
        toggleDrawer={onClose}
        className={clsx(
          "bottom-0 top-auto !h-[70%] !w-full rounded-[16px_16px_0px_0px] bg-[#141518] md:rounded-none",
          "md:top-0 md:!h-full md:!w-[375px]"
        )}
        direction={isMobile ? "bottom" : "right"}
      >
        <div className="border-white-50 h-[calc(100%-70px)] w-full rounded-[16px_16px_0px_0px] border bg-[#141518] md:rounded-none">
          <div className="border-white-100 flex h-[50px] items-center justify-between border-b px-[16px] py-[12px]">
            <div className="body-md-medium-14 md:heading-sm-medium-16 flex items-center gap-2">
              <Setting className="h-[20px] w-[20px]" /> {title}
            </div>
            <div className="cursor-pointer" onClick={onClose}>
              <CloseIcon />
            </div>
          </div>

          <div className="customer-scroll h-[calc(100%-50px)] overflow-auto">
            <div className="border-white-100 border-b p-[16px]">
              <div className="body-sm-regular-12 text-white-500 mb-[8px] flex items-center justify-between">
                <div>Dex & Platform</div>
                <div
                  className="flex cursor-pointer items-center gap-2"
                  onClick={onSelectAllDex}
                >
                  Select all {_renderCheckbox(isSelectedAllDex)}
                </div>
              </div>
              <div className="flex flex-wrap gap-2">
                {MEME_DEXES.map((item, index) => {
                  return (
                    <div
                      onClick={() => onSelectDex(item)}
                      key={index}
                      className={`
                    ${
                      listDexSelected.includes(item)
                        ? "bg-brand-900 border-brand-800"
                        : "border-white-50"
                    }
                    body-sm-regular-12 hover:border-brand-800 flex h-[36px] cursor-pointer items-center gap-2 rounded-[6px] border px-[8px]`}
                    >
                      <Image
                        src={getDexLogoUrl(item as keyof typeof DEXS)}
                        className="h-[16px] w-[16px] rounded-full"
                        width={16}
                        height={16}
                        alt={getDexName(item as keyof typeof DEXS)}
                        unoptimized
                      />
                      {getDexName(item as keyof typeof DEXS)}
                    </div>
                  );
                })}
              </div>
            </div>

            <div className="p-[16px] ">
              <div className="body-sm-regular-12 text-white-500 mb-[8px]">
                Advanced settings
              </div>
              <div>
                <FilterAdvanced
                  params={paramsAdvanced}
                  setParams={setSetParamsAdvanced}
                />
              </div>
            </div>
          </div>
        </div>
      </AppDrawer>
      {isOpen && (
        <div className="border-white-100 fixed bottom-0 right-0 z-[9999] flex h-[70px] w-full items-center justify-between border-t bg-[#141518] p-[16px] md:w-[375px]">
          <div
            className="body-md-medium-14 flex cursor-pointer items-center gap-2"
            onClick={onRefresh}
          >
            <ReloadIcon className="h-[18px] w-[18px]" />
            Refresh
          </div>
          <AppButton size="large" onClick={onFilter}>
            Apply filters
          </AppButton>
        </div>
      )}
    </>
  );
};
